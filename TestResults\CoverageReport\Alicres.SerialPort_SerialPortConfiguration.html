<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=1" />
<link href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAn1BMVEUAAADCAAAAAAA3yDfUAAA3yDfUAAA8PDzr6+sAAAD4+Pg3yDeQkJDTAADt7e3V1dU3yDdCQkIAAADbMTHUAABBykHUAAA2yDY3yDfr6+vTAAB3diDR0dGYcHDUAAAjhiPSAAA3yDeuAADUAAA3yDf////OCALg9+BLzktBuzRelimzKgv87+/dNTVflSn1/PWz6rO126g5yDlYniy0KgwjJ0TyAAAAI3RSTlMABAj0WD6rJcsN7X1HzMqUJyYW+/X08+bltqSeaVRBOy0cE+citBEAAADBSURBVDjLlczXEoIwFIThJPYGiL0XiL3r+z+bBOJs9JDMuLffP8v+Gxfc6aIyDQVjQcnqnvRDEQwLJYtXpZT+YhDHKIjLbS+OUeT4TjkKi6OwOArq+yeKXD9uDqQQbcOjyCy0e6bTojZSftX+U6zUQ7OuittDu1k0WHqRFfdXQijgjKfF6ZwAikvmKD6OQjmKWUcDigkztm5FZN05nMON9ZcoinlBmTNnAUdBnRbUUbgdBZwWbkcBpwXcVsBtxfjb31j1QB5qeebOAAAAAElFTkSuQmCC" rel="icon" type="image/x-icon" />
<title>Alicres.SerialPort.Models.SerialPortConfiguration - Coverage Report</title>
<link rel="stylesheet" type="text/css" href="report.css" />
</head><body><div class="container"><div class="containerleft">
<h1><a href="index.html" class="back">&lt;</a> Summary</h1>
<div class="card-group">
<div class="card">
<div class="card-header">Information</div>
<div class="card-body">
<div class="table">
<table>
<tr>
<th>Class:</th>
<td class="limit-width " title="Alicres.SerialPort.Models.SerialPortConfiguration">Alicres.SerialPort.Models.SerialPortConfiguration</td>
</tr>
<tr>
<th>Assembly:</th>
<td class="limit-width " title="Alicres.SerialPort">Alicres.SerialPort</td>
</tr>
<tr>
<th>File(s):</th>
<td class="overflow-wrap"><a href="#GAlicressrcAlicresSerialPortModelsSerialPortConfigurationcs" class="navigatetohash">G:\Alicres\src\Alicres.SerialPort\Models\SerialPortConfiguration.cs</a></td>
</tr>
</table>
</div>
</div>
</div>
</div>
<div class="card-group">
<div class="card">
<div class="card-header">Line coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar0">100%</div>
<div class="table">
<table>
<tr>
<th>Covered lines:</th>
<td class="limit-width right" title="37">37</td>
</tr>
<tr>
<th>Uncovered lines:</th>
<td class="limit-width right" title="0">0</td>
</tr>
<tr>
<th>Coverable lines:</th>
<td class="limit-width right" title="37">37</td>
</tr>
<tr>
<th>Total lines:</th>
<td class="limit-width right" title="140">140</td>
</tr>
<tr>
<th>Line coverage:</th>
<td class="limit-width right" title="37 of 37">100%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Branch coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar0">100%</div>
<div class="table">
<table>
<tr>
<th>Covered branches:</th>
<td class="limit-width right" title="18">18</td>
</tr>
<tr>
<th>Total branches:</th>
<td class="limit-width right" title="18">18</td>
</tr>
<tr>
<th>Branch coverage:</th>
<td class="limit-width right" title="18 of 18">100%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Method coverage</div>
<div class="card-body">
<div class="center">
<p>Feature is only available for sponsors</p>
<a class="pro-button" href="https://reportgenerator.io/pro" target="_blank">Upgrade to PRO version</a>
</div>
</div>
</div>
</div>
<h1>Metrics</h1>
<div class="table-responsive">
<table class="overview table-fixed">
<colgroup>
<col class="column-min-200" />
<col class="column105" />
<col class="column105" />
<col class="column105" />
<col class="column105" />
</colgroup>
<thead><tr><th>Method</th><th>Branch coverage <a href="https://en.wikipedia.org/wiki/Code_coverage" target="_blank"><i class="icon-info-circled"></i></a></th><th>Crap Score <a href="https://googletesting.blogspot.de/2011/02/this-code-is-crap.html" target="_blank"><i class="icon-info-circled"></i></a></th><th>Cyclomatic complexity <a href="https://en.wikipedia.org/wiki/Cyclomatic_complexity" target="_blank"><i class="icon-info-circled"></i></a></th><th>Line coverage <a href="https://en.wikipedia.org/wiki/Code_coverage" target="_blank"><i class="icon-info-circled"></i></a></th></tr></thead>
<tbody>
<tr><td title="get_PortName()"><a href="#file0_line14" class="navigatetohash">get_PortName()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_BaudRate()"><a href="#file0_line19" class="navigatetohash">get_BaudRate()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_DataBits()"><a href="#file0_line24" class="navigatetohash">get_DataBits()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_StopBits()"><a href="#file0_line29" class="navigatetohash">get_StopBits()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_Parity()"><a href="#file0_line34" class="navigatetohash">get_Parity()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_Handshake()"><a href="#file0_line39" class="navigatetohash">get_Handshake()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_ReadTimeout()"><a href="#file0_line44" class="navigatetohash">get_ReadTimeout()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_WriteTimeout()"><a href="#file0_line49" class="navigatetohash">get_WriteTimeout()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_ReceiveBufferSize()"><a href="#file0_line54" class="navigatetohash">get_ReceiveBufferSize()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_SendBufferSize()"><a href="#file0_line59" class="navigatetohash">get_SendBufferSize()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_DtrEnable()"><a href="#file0_line64" class="navigatetohash">get_DtrEnable()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_RtsEnable()"><a href="#file0_line69" class="navigatetohash">get_RtsEnable()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_EnableAutoReconnect()"><a href="#file0_line74" class="navigatetohash">get_EnableAutoReconnect()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_ReconnectInterval()"><a href="#file0_line79" class="navigatetohash">get_ReconnectInterval()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_MaxReconnectAttempts()"><a href="#file0_line84" class="navigatetohash">get_MaxReconnectAttempts()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_EnableAdvancedBuffering()"><a href="#file0_line89" class="navigatetohash">get_EnableAdvancedBuffering()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_DataQueueMaxLength()"><a href="#file0_line94" class="navigatetohash">get_DataQueueMaxLength()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_BufferOverflowStrategy()"><a href="#file0_line99" class="navigatetohash">get_BufferOverflowStrategy()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_BufferCleanupInterval()"><a href="#file0_line104" class="navigatetohash">get_BufferCleanupInterval()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="get_BufferWarningThreshold()"><a href="#file0_line109" class="navigatetohash">get_BufferWarningThreshold()</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
<tr><td title="IsValid()"><a href="#file0_line116" class="navigatetohash">IsValid()</a></td><td>100%</td><td>18</td><td>18</td><td>100%</td></tr>
<tr><td title="CreateDefault(System.String)"><a href="#file0_line134" class="navigatetohash">CreateDefault(...)</a></td><td>100%</td><td>1</td><td>1</td><td>100%</td></tr>
</tbody>
</table>
</div>
<h1>File(s)</h1>
<h2 id="GAlicressrcAlicresSerialPortModelsSerialPortConfigurationcs">G:\Alicres\src\Alicres.SerialPort\Models\SerialPortConfiguration.cs</h2>
<div class="table-responsive">
<table class="lineAnalysis">
<thead><tr><th></th><th>#</th><th>Line</th><th></th><th>Line coverage</th></tr></thead>
<tbody>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line1"></a><code>1</code></td><td></td><td class="lightgray"><code>using&nbsp;System.IO.Ports;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line2"></a><code>2</code></td><td></td><td class="lightgray"><code>using&nbsp;Alicres.SerialPort.Constants;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line3"></a><code>3</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line4"></a><code>4</code></td><td></td><td class="lightgray"><code>namespace&nbsp;Alicres.SerialPort.Models;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line5"></a><code>5</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line6"></a><code>6</code></td><td></td><td class="lightgray"><code>///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line7"></a><code>7</code></td><td></td><td class="lightgray"><code>///&nbsp;串口配置信息</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line8"></a><code>8</code></td><td></td><td class="lightgray"><code>///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line9"></a><code>9</code></td><td></td><td class="lightgray"><code>public&nbsp;class&nbsp;SerialPortConfiguration</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line10"></a><code>10</code></td><td></td><td class="lightgray"><code>{</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line11"></a><code>11</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line12"></a><code>12</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;端口名称（如&nbsp;COM1,&nbsp;COM2&nbsp;等）</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line13"></a><code>13</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (6126 visits)" data-coverage="{'AllTestMethods': {'VC': '6126', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">6126</td><td class="rightmargin right"><a id="file0_line14"></a><code>14</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;string&nbsp;PortName&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;string.Empty;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line15"></a><code>15</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line16"></a><code>16</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line17"></a><code>17</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;波特率</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line18"></a><code>18</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (3487 visits)" data-coverage="{'AllTestMethods': {'VC': '3487', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">3487</td><td class="rightmargin right"><a id="file0_line19"></a><code>19</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;BaudRate&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.BaudRate;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line20"></a><code>20</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line21"></a><code>21</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line22"></a><code>22</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;数据位</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line23"></a><code>23</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (3819 visits)" data-coverage="{'AllTestMethods': {'VC': '3819', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">3819</td><td class="rightmargin right"><a id="file0_line24"></a><code>24</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;DataBits&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.DataBits;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line25"></a><code>25</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line26"></a><code>26</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line27"></a><code>27</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;停止位</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line28"></a><code>28</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2177 visits)" data-coverage="{'AllTestMethods': {'VC': '2177', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2177</td><td class="rightmargin right"><a id="file0_line29"></a><code>29</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;StopBits&nbsp;StopBits&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;StopBits.One;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line30"></a><code>30</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line31"></a><code>31</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line32"></a><code>32</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;校验位</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line33"></a><code>33</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2177 visits)" data-coverage="{'AllTestMethods': {'VC': '2177', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2177</td><td class="rightmargin right"><a id="file0_line34"></a><code>34</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;Parity&nbsp;Parity&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;Parity.None;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line35"></a><code>35</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line36"></a><code>36</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line37"></a><code>37</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;握手协议</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line38"></a><code>38</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (1840 visits)" data-coverage="{'AllTestMethods': {'VC': '1840', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">1840</td><td class="rightmargin right"><a id="file0_line39"></a><code>39</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;Handshake&nbsp;Handshake&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;Handshake.None;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line40"></a><code>40</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line41"></a><code>41</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line42"></a><code>42</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;读取超时时间（毫秒）</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line43"></a><code>43</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2942 visits)" data-coverage="{'AllTestMethods': {'VC': '2942', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2942</td><td class="rightmargin right"><a id="file0_line44"></a><code>44</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;ReadTimeout&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.ReadTimeout;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line45"></a><code>45</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line46"></a><code>46</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line47"></a><code>47</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;写入超时时间（毫秒）</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line48"></a><code>48</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2926 visits)" data-coverage="{'AllTestMethods': {'VC': '2926', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2926</td><td class="rightmargin right"><a id="file0_line49"></a><code>49</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;WriteTimeout&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.WriteTimeout;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line50"></a><code>50</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line51"></a><code>51</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line52"></a><code>52</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;接收缓冲区大小</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line53"></a><code>53</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2922 visits)" data-coverage="{'AllTestMethods': {'VC': '2922', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2922</td><td class="rightmargin right"><a id="file0_line54"></a><code>54</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;ReceiveBufferSize&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.ReceiveBufferSize;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line55"></a><code>55</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line56"></a><code>56</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line57"></a><code>57</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;发送缓冲区大小</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line58"></a><code>58</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2890 visits)" data-coverage="{'AllTestMethods': {'VC': '2890', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2890</td><td class="rightmargin right"><a id="file0_line59"></a><code>59</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;SendBufferSize&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.SendBufferSize;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line60"></a><code>60</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line61"></a><code>61</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line62"></a><code>62</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;是否启用数据终端就绪信号</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line63"></a><code>63</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (1840 visits)" data-coverage="{'AllTestMethods': {'VC': '1840', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">1840</td><td class="rightmargin right"><a id="file0_line64"></a><code>64</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;bool&nbsp;DtrEnable&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;false;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line65"></a><code>65</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line66"></a><code>66</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line67"></a><code>67</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;是否启用请求发送信号</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line68"></a><code>68</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (1840 visits)" data-coverage="{'AllTestMethods': {'VC': '1840', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">1840</td><td class="rightmargin right"><a id="file0_line69"></a><code>69</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;bool&nbsp;RtsEnable&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;false;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line70"></a><code>70</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line71"></a><code>71</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line72"></a><code>72</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;是否启用自动重连</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line73"></a><code>73</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (1846 visits)" data-coverage="{'AllTestMethods': {'VC': '1846', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">1846</td><td class="rightmargin right"><a id="file0_line74"></a><code>74</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;bool&nbsp;EnableAutoReconnect&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;false;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line75"></a><code>75</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line76"></a><code>76</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line77"></a><code>77</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;重连间隔时间（毫秒）</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line78"></a><code>78</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2605 visits)" data-coverage="{'AllTestMethods': {'VC': '2605', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2605</td><td class="rightmargin right"><a id="file0_line79"></a><code>79</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;ReconnectInterval&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.ReconnectInterval;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line80"></a><code>80</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line81"></a><code>81</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line82"></a><code>82</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;最大重连次数</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line83"></a><code>83</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2589 visits)" data-coverage="{'AllTestMethods': {'VC': '2589', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2589</td><td class="rightmargin right"><a id="file0_line84"></a><code>84</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;MaxReconnectAttempts&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.MaxReconnectAttempts;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line85"></a><code>85</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line86"></a><code>86</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line87"></a><code>87</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;是否启用高级缓冲管理</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line88"></a><code>88</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (2218 visits)" data-coverage="{'AllTestMethods': {'VC': '2218', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">2218</td><td class="rightmargin right"><a id="file0_line89"></a><code>89</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;bool&nbsp;EnableAdvancedBuffering&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;false;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line90"></a><code>90</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line91"></a><code>91</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line92"></a><code>92</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;内部数据队列最大长度</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line93"></a><code>93</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (8717051 visits)" data-coverage="{'AllTestMethods': {'VC': '8717051', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">8717051</td><td class="rightmargin right"><a id="file0_line94"></a><code>94</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;DataQueueMaxLength&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.DataQueueMaxLength;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line95"></a><code>95</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line96"></a><code>96</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line97"></a><code>97</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;缓冲区溢出处理策略</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line98"></a><code>98</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (3752398 visits)" data-coverage="{'AllTestMethods': {'VC': '3752398', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">3752398</td><td class="rightmargin right"><a id="file0_line99"></a><code>99</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;BufferOverflowStrategy&nbsp;BufferOverflowStrategy&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;BufferOverflowStrategy.DropOldest;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line100"></a><code>100</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line101"></a><code>101</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line102"></a><code>102</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;自动清理缓冲区的时间间隔（毫秒）</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line103"></a><code>103</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (3456 visits)" data-coverage="{'AllTestMethods': {'VC': '3456', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">3456</td><td class="rightmargin right"><a id="file0_line104"></a><code>104</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;BufferCleanupInterval&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.BufferCleanupInterval;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line105"></a><code>105</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line106"></a><code>106</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line107"></a><code>107</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;缓冲区使用率警告阈值（百分比）</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line108"></a><code>108</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="coverableline" title="Covered (433829 visits)" data-coverage="{'AllTestMethods': {'VC': '433829', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">433829</td><td class="rightmargin right"><a id="file0_line109"></a><code>109</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;int&nbsp;BufferWarningThreshold&nbsp;{&nbsp;get;&nbsp;set;&nbsp;}&nbsp;=&nbsp;SerialPortConstants.Defaults.BufferWarningThreshold;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line110"></a><code>110</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line111"></a><code>111</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line112"></a><code>112</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;验证配置是否有效</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line113"></a><code>113</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line114"></a><code>114</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;returns&gt;如果配置有效返回&nbsp;true，否则返回&nbsp;false&lt;/returns&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line115"></a><code>115</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;bool&nbsp;IsValid()</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line116"></a><code>116</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Covered (875 visits, 18 of 18 branches are covered)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line117"></a><code>117</code></td><td class="percentagebar percentagebar100"><i class="icon-fork"></i></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;!string.IsNullOrWhiteSpace(PortName)&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line118"></a><code>118</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;BaudRate&nbsp;&gt;&nbsp;0&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line119"></a><code>119</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBits&nbsp;&gt;=&nbsp;5&nbsp;&amp;&amp;&nbsp;DataBits&nbsp;&lt;=&nbsp;8&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line120"></a><code>120</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ReadTimeout&nbsp;&gt;=&nbsp;0&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line121"></a><code>121</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;WriteTimeout&nbsp;&gt;=&nbsp;0&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line122"></a><code>122</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ReceiveBufferSize&nbsp;&gt;&nbsp;0&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line123"></a><code>123</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SendBufferSize&nbsp;&gt;&nbsp;0&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line124"></a><code>124</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ReconnectInterval&nbsp;&gt;=&nbsp;0&nbsp;&amp;&amp;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line125"></a><code>125</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MaxReconnectAttempts&nbsp;&gt;=&nbsp;0;</code></td></tr>
<tr class="coverableline" title="Covered (875 visits)" data-coverage="{'AllTestMethods': {'VC': '875', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">875</td><td class="rightmargin right"><a id="file0_line126"></a><code>126</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line127"></a><code>127</code></td><td></td><td class="lightgray"><code></code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line128"></a><code>128</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line129"></a><code>129</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;创建默认配置</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line130"></a><code>130</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;/summary&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line131"></a><code>131</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;param&nbsp;name=&quot;portName&quot;&gt;端口名称&lt;/param&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line132"></a><code>132</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;///&nbsp;&lt;returns&gt;默认配置实例&lt;/returns&gt;</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line133"></a><code>133</code></td><td></td><td class="lightgray"><code>&nbsp;&nbsp;&nbsp;&nbsp;public&nbsp;static&nbsp;SerialPortConfiguration&nbsp;CreateDefault(string&nbsp;portName)</code></td></tr>
<tr class="coverableline" title="Covered (32 visits)" data-coverage="{'AllTestMethods': {'VC': '32', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">32</td><td class="rightmargin right"><a id="file0_line134"></a><code>134</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Covered (32 visits)" data-coverage="{'AllTestMethods': {'VC': '32', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">32</td><td class="rightmargin right"><a id="file0_line135"></a><code>135</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return&nbsp;new&nbsp;SerialPortConfiguration</code></td></tr>
<tr class="coverableline" title="Covered (32 visits)" data-coverage="{'AllTestMethods': {'VC': '32', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">32</td><td class="rightmargin right"><a id="file0_line136"></a><code>136</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</code></td></tr>
<tr class="coverableline" title="Covered (32 visits)" data-coverage="{'AllTestMethods': {'VC': '32', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">32</td><td class="rightmargin right"><a id="file0_line137"></a><code>137</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PortName&nbsp;=&nbsp;portName</code></td></tr>
<tr class="coverableline" title="Covered (32 visits)" data-coverage="{'AllTestMethods': {'VC': '32', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">32</td><td class="rightmargin right"><a id="file0_line138"></a><code>138</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;};</code></td></tr>
<tr class="coverableline" title="Covered (32 visits)" data-coverage="{'AllTestMethods': {'VC': '32', 'LVS': 'green'}}"><td class="green">&nbsp;</td><td class="leftmargin rightmargin right">32</td><td class="rightmargin right"><a id="file0_line139"></a><code>139</code></td><td></td><td class="lightgreen"><code>&nbsp;&nbsp;&nbsp;&nbsp;}</code></td></tr>
<tr class="" title="Not coverable" data-coverage="{'AllTestMethods': {'VC': '', 'LVS': 'gray'}}"><td class="gray">&nbsp;</td><td class="leftmargin rightmargin right"></td><td class="rightmargin right"><a id="file0_line140"></a><code>140</code></td><td></td><td class="lightgray"><code>}</code></td></tr>
</tbody>
</table>
</div>
<div class="footer">Generated by: ReportGenerator 5.4.7.0<br />2025/6/12 - 11:17:48<br /><a href="https://github.com/danielpalme/ReportGenerator">GitHub</a> | <a href="https://reportgenerator.io">reportgenerator.io</a></div></div>
<div class="containerright">
<div class="containerrightfixed">
<h1>Methods/Properties</h1>
<a href="#file0_line14" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_PortName()"><i class="icon-wrench"></i>get_PortName()</a><br />
<a href="#file0_line19" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_BaudRate()"><i class="icon-wrench"></i>get_BaudRate()</a><br />
<a href="#file0_line24" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_DataBits()"><i class="icon-wrench"></i>get_DataBits()</a><br />
<a href="#file0_line29" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_StopBits()"><i class="icon-wrench"></i>get_StopBits()</a><br />
<a href="#file0_line34" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Parity()"><i class="icon-wrench"></i>get_Parity()</a><br />
<a href="#file0_line39" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_Handshake()"><i class="icon-wrench"></i>get_Handshake()</a><br />
<a href="#file0_line44" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_ReadTimeout()"><i class="icon-wrench"></i>get_ReadTimeout()</a><br />
<a href="#file0_line49" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_WriteTimeout()"><i class="icon-wrench"></i>get_WriteTimeout()</a><br />
<a href="#file0_line54" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_ReceiveBufferSize()"><i class="icon-wrench"></i>get_ReceiveBufferSize()</a><br />
<a href="#file0_line59" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_SendBufferSize()"><i class="icon-wrench"></i>get_SendBufferSize()</a><br />
<a href="#file0_line64" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_DtrEnable()"><i class="icon-wrench"></i>get_DtrEnable()</a><br />
<a href="#file0_line69" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_RtsEnable()"><i class="icon-wrench"></i>get_RtsEnable()</a><br />
<a href="#file0_line74" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_EnableAutoReconnect()"><i class="icon-wrench"></i>get_EnableAutoReconnect()</a><br />
<a href="#file0_line79" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_ReconnectInterval()"><i class="icon-wrench"></i>get_ReconnectInterval()</a><br />
<a href="#file0_line84" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_MaxReconnectAttempts()"><i class="icon-wrench"></i>get_MaxReconnectAttempts()</a><br />
<a href="#file0_line89" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_EnableAdvancedBuffering()"><i class="icon-wrench"></i>get_EnableAdvancedBuffering()</a><br />
<a href="#file0_line94" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_DataQueueMaxLength()"><i class="icon-wrench"></i>get_DataQueueMaxLength()</a><br />
<a href="#file0_line99" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_BufferOverflowStrategy()"><i class="icon-wrench"></i>get_BufferOverflowStrategy()</a><br />
<a href="#file0_line104" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_BufferCleanupInterval()"><i class="icon-wrench"></i>get_BufferCleanupInterval()</a><br />
<a href="#file0_line109" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - get_BufferWarningThreshold()"><i class="icon-wrench"></i>get_BufferWarningThreshold()</a><br />
<a href="#file0_line116" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - IsValid()"><i class="icon-cube"></i>IsValid()</a><br />
<a href="#file0_line134" class="navigatetohash percentagebar percentagebar100" title="Line coverage: 100% - CreateDefault(System.String)"><i class="icon-cube"></i>CreateDefault(System.String)</a><br />
<br/></div>
</div></div>
<script type="text/javascript">
/* <![CDATA[ */
(function() {
    var url = window.location.href;
    var startOfQueryString = url.indexOf('?');
    var queryString = startOfQueryString > -1 ? url.substr(startOfQueryString) : '';

    if (startOfQueryString > -1) {
        var i = 0, href= null;
        var css = document.getElementsByTagName('link');

        for (i = 0; i < css.length; i++) {
            if (css[i].getAttribute('rel') !== 'stylesheet') {
            continue;
            }

            href = css[i].getAttribute('href');

            if (href) {
            css[i].setAttribute('href', href + queryString);
            }
        }

        var links = document.getElementsByTagName('a');

        for (i = 0; i < links.length; i++) {
            href = links[i].getAttribute('href');

            if (href
                && !href.startsWith('http://')
                && !href.startsWith('https://')
                && !href.startsWith('#')
                && href.indexOf('?') === -1) {
            links[i].setAttribute('href', href + queryString);
            }
        }
    }

    var newScript = document.createElement('script');
    newScript.src = 'class.js' + queryString;
    document.getElementsByTagName('body')[0].appendChild(newScript);
})();
/* ]]> */ 
</script></body></html>