using Alicres.SerialPort.Exceptions;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Tests.Integration.TestHelpers;
using Alicres.SerialPort.Tests.TestHelpers;
using FluentAssertions;
using System.IO.Ports;
using Xunit;

namespace Alicres.SerialPort.Tests.Integration;

/// <summary>
/// 错误恢复和边界条件测试
/// </summary>
public class ErrorRecoveryTests : IntegrationTestBase
{
    /// <summary>
    /// 测试串口断开连接的错误恢复
    /// </summary>
    [Fact]
    public async Task SerialPort_ConnectionLost_ShouldHandleGracefully()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_DISCONNECT_TEST");
        var service = CreateSerialPortService(configuration);
        var mockService = service as MockSerialPortService;
        var mockPort = mockService!.GetMockSerialPort();

        var errorCollector = CreateEventCollector<SerialPortErrorEventArgs>();
        var statusCollector = CreateEventCollector<SerialPortStatusChangedEventArgs>();
        
        service.ErrorOccurred += errorCollector.EventHandler;
        service.StatusChanged += statusCollector.EventHandler;

        try
        {
            // Act & Assert - 步骤1：正常连接
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // 步骤2：模拟连接丢失
            mockPort.SimulateError(SerialError.Frame);
            
            // 验证错误事件触发
            var errorReceived = await errorCollector.WaitForEventsAsync(1, TimeSpan.FromSeconds(3));
            errorReceived.Should().BeTrue("应该触发错误事件");

            var errorEvent = errorCollector.GetFirstEvent();
            errorEvent.Should().NotBeNull();
            errorEvent!.Args.Exception.Should().NotBeNull();

            // 步骤3：验证服务状态
            service.Status.ErrorCount.Should().BeGreaterThan(0, "应该记录错误次数");
            service.Status.LastError.Should().NotBeNullOrEmpty("应该记录最后的错误信息");

            // 步骤4：验证服务仍然可以正常操作
            var testData = TestDataGenerator.GenerateTestData(100);
            var sentBytes = await service.SendAsync(testData);
            sentBytes.Should().Be(testData.Length, "错误恢复后应该能正常发送数据");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试设备移除场景
    /// </summary>
    [Fact]
    public async Task SerialPort_DeviceRemoved_ShouldDetectAndRecover()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_DEVICE_REMOVED");
        configuration.EnableAutoReconnect = true;
        configuration.ReconnectInterval = 1000; // 1秒重连间隔
        configuration.MaxReconnectAttempts = 3;

        var service = CreateSerialPortService(configuration);
        var mockService = service as MockSerialPortService;
        var mockPort = mockService!.GetMockSerialPort();

        var statusCollector = CreateEventCollector<SerialPortStatusChangedEventArgs>();
        service.StatusChanged += statusCollector.EventHandler;

        try
        {
            // Act & Assert - 步骤1：正常连接
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // 步骤2：模拟设备移除（强制关闭）
            mockPort.Close();
            
            // 等待状态变化
            await Task.Delay(500);

            // 步骤3：验证状态更新
            service.Status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);

            // 步骤4：尝试重新连接
            var reconnectResult = await service.OpenAsync();
            reconnectResult.Should().BeTrue("应该能够重新连接");
            service.IsConnected.Should().BeTrue();
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试超时处理
    /// </summary>
    [Theory]
    [InlineData(100)]   // 短超时
    [InlineData(1000)]  // 标准超时
    [InlineData(5000)]  // 长超时
    public async Task SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(int timeoutMs)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_TIMEOUT_TEST");
        configuration.ReadTimeout = timeoutMs;
        configuration.WriteTimeout = timeoutMs;

        var service = CreateSerialPortService(configuration);

        try
        {
            // Act & Assert
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // 测试读取超时（没有数据可读）
            var buffer = new byte[1024];
            var startTime = DateTime.UtcNow;
            
            var bytesRead = await service.ReadAsync(buffer, 0, buffer.Length);
            var elapsed = DateTime.UtcNow - startTime;

            // 验证超时行为
            bytesRead.Should().Be(0, "没有数据时应该返回0");
            elapsed.Should().BeLessThan(TimeSpan.FromMilliseconds(timeoutMs + 1000), 
                "读取操作应该在超时时间内完成");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试缓冲区溢出处理
    /// </summary>
    [Fact]
    public async Task SerialPort_BufferOverflow_ShouldHandleGracefully()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateAdvancedBufferingConfiguration("COM_BUFFER_OVERFLOW");
        configuration.ReceiveBufferSize = 1024; // 小缓冲区
        configuration.BufferOverflowStrategy = BufferOverflowStrategy.DropOldest;

        var service = CreateSerialPortService(configuration);
        var mockService = service as MockSerialPortService;
        var mockPort = mockService!.GetMockSerialPort();

        var dataCollector = CreateEventCollector<SerialPortDataReceivedEventArgs>();
        service.DataReceived += dataCollector.EventHandler;

        try
        {
            // Act & Assert
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // 发送大量数据导致缓冲区溢出
            var largeData = TestDataGenerator.GenerateTestData(2048); // 超过缓冲区大小
            mockPort.SimulateDataReceived(largeData);

            // 等待数据处理
            await Task.Delay(500);

            // 验证缓冲区溢出处理
            var dataReceived = await dataCollector.WaitForEventsAsync(1, TimeSpan.FromSeconds(3));
            dataReceived.Should().BeTrue("应该接收到数据事件");

            // 验证数据完整性（根据溢出策略）
            var receivedEvent = dataCollector.GetFirstEvent();
            receivedEvent.Should().NotBeNull();
            receivedEvent!.Args.Data.RawData.Should().NotBeEmpty("应该接收到部分数据");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试无效配置处理
    /// </summary>
    [Theory]
    [InlineData("")]           // 空端口名
    [InlineData("INVALID")]    // 无效端口名
    [InlineData("COM999")]     // 不存在的端口
    public async Task SerialPort_InvalidConfiguration_ShouldThrowException(string portName)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration(portName);
        var service = CreateSerialPortService(configuration);

        try
        {
            // Act & Assert
            if (string.IsNullOrEmpty(portName))
            {
                // 空端口名应该在配置时就抛出异常
                var configAction = () => service.Configure(configuration);
                configAction.Should().Throw<SerialPortConfigurationException>();
            }
            else
            {
                // 无效端口名应该在打开时抛出异常
                var openResult = await service.OpenAsync();
                openResult.Should().BeFalse("无效端口应该打开失败");
                service.IsConnected.Should().BeFalse();
            }
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试并发访问安全性
    /// </summary>
    [Fact]
    public async Task SerialPort_ConcurrentAccess_ShouldBeThreadSafe()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_CONCURRENT_SAFE");
        var service = CreateSerialPortService(configuration);

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // Act - 并发执行多种操作
            var tasks = new List<Task>();

            // 并发发送数据
            for (int i = 0; i < 10; i++)
            {
                var data = TestDataGenerator.GenerateTestData(100);
                tasks.Add(service.SendAsync(data));
            }

            // 并发读取数据
            for (int i = 0; i < 5; i++)
            {
                tasks.Add(Task.Run(async () =>
                {
                    var buffer = new byte[100];
                    await service.ReadAsync(buffer, 0, buffer.Length);
                }));
            }

            // 并发状态查询
            for (int i = 0; i < 5; i++)
            {
                tasks.Add(Task.Run(() =>
                {
                    var isConnected = service.IsConnected;
                    var status = service.Status;
                    return Task.CompletedTask;
                }));
            }

            // Assert - 所有操作都应该成功完成
            await Task.WhenAll(tasks);

            // 验证服务状态仍然正常
            service.IsConnected.Should().BeTrue("并发操作后服务应该仍然正常");
            service.Status.BytesSent.Should().BeGreaterThan(0, "应该记录发送的数据");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试内存泄漏预防
    /// </summary>
    [Fact]
    public async Task SerialPort_RepeatedOperations_ShouldNotLeakMemory()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_MEMORY_TEST");
        
        // Act & Assert - 重复创建和销毁服务
        for (int i = 0; i < 10; i++)
        {
            using var service = CreateSerialPortService(configuration);
            
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            var testData = TestDataGenerator.GenerateTestData(1024);
            await service.SendAsync(testData);

            await service.CloseAsync();
            service.IsConnected.Should().BeFalse();
        }

        // 强制垃圾回收
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        // 验证内存使用（这里主要是确保没有异常）
        var memoryBefore = GC.GetTotalMemory(false);
        
        // 再次执行操作
        using var finalService = CreateSerialPortService(configuration);
        await finalService.OpenAsync();
        await finalService.SendAsync(TestDataGenerator.GenerateTestData(1024));
        await finalService.CloseAsync();

        var memoryAfter = GC.GetTotalMemory(true);
        
        // 内存增长应该在合理范围内
        var memoryGrowth = memoryAfter - memoryBefore;
        memoryGrowth.Should().BeLessThan(1024 * 1024, "内存增长应该小于1MB"); // 允许1MB的增长
    }
}
