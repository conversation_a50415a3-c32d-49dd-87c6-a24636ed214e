using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Alicres.SerialPort.Tests.TestHelpers;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;

namespace Alicres.SerialPort.Tests.Integration.TestHelpers;

/// <summary>
/// 集成测试基础类
/// </summary>
public abstract class IntegrationTestBase : IDisposable
{
    protected readonly IServiceProvider ServiceProvider;
    protected readonly ILogger<SerialPortService> SerialPortLogger;
    protected readonly ILogger<SerialPortManager> ManagerLogger;
    protected readonly List<IDisposable> DisposableResources;

    /// <summary>
    /// 构造函数
    /// </summary>
    protected IntegrationTestBase()
    {
        DisposableResources = new List<IDisposable>();
        
        // 设置服务容器
        var services = new ServiceCollection();
        ConfigureServices(services);
        ServiceProvider = services.BuildServiceProvider();
        
        // 获取日志记录器
        SerialPortLogger = ServiceProvider.GetRequiredService<ILogger<SerialPortService>>();
        ManagerLogger = ServiceProvider.GetRequiredService<ILogger<SerialPortManager>>();
    }

    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    protected virtual void ConfigureServices(IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.SetMinimumLevel(LogLevel.Debug);
            builder.AddConsole();
        });

        // 注册Mock服务的日志记录器
        services.AddSingleton<ILogger<MockSerialPortService>>(provider =>
            provider.GetRequiredService<ILoggerFactory>().CreateLogger<MockSerialPortService>());
    }

    /// <summary>
    /// 创建串口服务
    /// </summary>
    /// <param name="configuration">配置</param>
    /// <param name="useMock">是否使用Mock实现</param>
    /// <returns>串口服务</returns>
    protected ISerialPortService CreateSerialPortService(SerialPortConfiguration configuration, bool useMock = true)
    {
        ISerialPortService service;

        if (useMock)
        {
            var mockLogger = ServiceProvider.GetRequiredService<ILogger<MockSerialPortService>>();
            service = new MockSerialPortService(configuration, mockLogger);
        }
        else
        {
            service = new SerialPortService(configuration, SerialPortLogger);
        }

        DisposableResources.Add(service);
        return service;
    }

    /// <summary>
    /// 创建串口管理器
    /// </summary>
    /// <returns>串口管理器</returns>
    protected ISerialPortManager CreateSerialPortManager()
    {
        var manager = new SerialPortManager(ManagerLogger, ServiceProvider);
        DisposableResources.Add(manager);
        return manager;
    }

    /// <summary>
    /// 等待条件满足
    /// </summary>
    /// <param name="condition">条件函数</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="interval">检查间隔</param>
    /// <returns>是否满足条件</returns>
    protected async Task<bool> WaitForConditionAsync(
        Func<bool> condition, 
        TimeSpan timeout, 
        TimeSpan? interval = null)
    {
        interval ??= TimeSpan.FromMilliseconds(100);
        var endTime = DateTime.UtcNow.Add(timeout);

        while (DateTime.UtcNow < endTime)
        {
            if (condition())
                return true;

            await Task.Delay(interval.Value);
        }

        return false;
    }

    /// <summary>
    /// 等待异步条件满足
    /// </summary>
    /// <param name="condition">异步条件函数</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="interval">检查间隔</param>
    /// <returns>是否满足条件</returns>
    protected async Task<bool> WaitForConditionAsync(
        Func<Task<bool>> condition, 
        TimeSpan timeout, 
        TimeSpan? interval = null)
    {
        interval ??= TimeSpan.FromMilliseconds(100);
        var endTime = DateTime.UtcNow.Add(timeout);

        while (DateTime.UtcNow < endTime)
        {
            if (await condition())
                return true;

            await Task.Delay(interval.Value);
        }

        return false;
    }

    /// <summary>
    /// 验证事件触发
    /// </summary>
    /// <typeparam name="T">事件参数类型</typeparam>
    /// <param name="eventSubscription">事件订阅动作</param>
    /// <param name="triggerAction">触发动作</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>事件参数</returns>
    protected async Task<T> VerifyEventTriggeredAsync<T>(
        Action<EventHandler<T>> eventSubscription,
        Func<Task> triggerAction,
        TimeSpan? timeout = null) where T : EventArgs
    {
        timeout ??= TimeSpan.FromSeconds(5);
        T? eventArgs = null;
        var eventTriggered = false;

        void EventHandler(object? sender, T args)
        {
            eventArgs = args;
            eventTriggered = true;
        }

        eventSubscription(EventHandler);

        try
        {
            await triggerAction();

            var success = await WaitForConditionAsync(() => eventTriggered, timeout.Value);
            success.Should().BeTrue("事件应该在超时时间内触发");

            return eventArgs!;
        }
        finally
        {
            // 这里需要取消订阅，但由于委托的特性，我们无法直接取消
            // 在实际使用中，调用者需要负责取消订阅
        }
    }

    /// <summary>
    /// 验证多个事件按顺序触发
    /// </summary>
    /// <param name="eventVerifications">事件验证列表</param>
    /// <param name="timeout">总超时时间</param>
    /// <returns>是否所有事件都按顺序触发</returns>
    protected async Task<bool> VerifyEventsSequenceAsync(
        List<Func<Task<bool>>> eventVerifications,
        TimeSpan? timeout = null)
    {
        timeout ??= TimeSpan.FromSeconds(10);
        var startTime = DateTime.UtcNow;

        foreach (var verification in eventVerifications)
        {
            var remainingTime = timeout.Value - (DateTime.UtcNow - startTime);
            if (remainingTime <= TimeSpan.Zero)
                return false;

            var success = await verification();
            if (!success)
                return false;
        }

        return true;
    }

    /// <summary>
    /// 创建事件收集器
    /// </summary>
    /// <typeparam name="T">事件参数类型</typeparam>
    /// <returns>事件收集器</returns>
    protected EventCollector<T> CreateEventCollector<T>() where T : EventArgs
    {
        var collector = new EventCollector<T>();
        DisposableResources.Add(collector);
        return collector;
    }

    /// <summary>
    /// 验证数据传输完整性
    /// </summary>
    /// <param name="originalData">原始数据</param>
    /// <param name="receivedData">接收数据</param>
    /// <param name="tolerance">容错字节数</param>
    protected void VerifyDataIntegrity(byte[] originalData, byte[] receivedData, int tolerance = 0)
    {
        if (tolerance == 0)
        {
            receivedData.Should().BeEquivalentTo(originalData, "接收的数据应该与发送的数据完全一致");
        }
        else
        {
            var difference = Math.Abs(originalData.Length - receivedData.Length);
            difference.Should().BeLessOrEqualTo(tolerance, $"数据长度差异应该在容错范围内（{tolerance}字节）");

            var minLength = Math.Min(originalData.Length, receivedData.Length);
            var matchingBytes = 0;
            
            for (int i = 0; i < minLength; i++)
            {
                if (originalData[i] == receivedData[i])
                    matchingBytes++;
            }

            var accuracy = (double)matchingBytes / minLength;
            accuracy.Should().BeGreaterThan(0.95, "数据准确率应该超过95%");
        }
    }

    /// <summary>
    /// 生成性能报告
    /// </summary>
    /// <param name="testName">测试名称</param>
    /// <param name="dataSize">数据大小</param>
    /// <param name="duration">持续时间</param>
    /// <param name="additionalMetrics">额外指标</param>
    protected void GeneratePerformanceReport(
        string testName,
        long dataSize,
        TimeSpan duration,
        Dictionary<string, object>? additionalMetrics = null)
    {
        var throughput = dataSize / duration.TotalSeconds;
        
        Console.WriteLine($"\n=== 性能报告: {testName} ===");
        Console.WriteLine($"数据大小: {dataSize:N0} 字节");
        Console.WriteLine($"传输时间: {duration.TotalMilliseconds:F2} 毫秒");
        Console.WriteLine($"吞吐量: {throughput:F2} 字节/秒 ({throughput / 1024:F2} KB/s)");
        
        if (additionalMetrics != null)
        {
            Console.WriteLine("额外指标:");
            foreach (var metric in additionalMetrics)
            {
                Console.WriteLine($"  {metric.Key}: {metric.Value}");
            }
        }
        
        Console.WriteLine("========================\n");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public virtual void Dispose()
    {
        foreach (var resource in DisposableResources)
        {
            try
            {
                resource?.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"释放资源时发生异常: {ex.Message}");
            }
        }
        
        DisposableResources.Clear();
        
        if (ServiceProvider is IDisposable disposableProvider)
        {
            disposableProvider.Dispose();
        }
    }
}
